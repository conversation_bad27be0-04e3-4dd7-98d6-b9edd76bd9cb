"use client";

import Description from '@/components/Description';
import Separator from '@/components/Separator';
import { useTranslation } from "@/hooks/useTranslation";

export default function Values({
  namespace = 'agency',
  value1TitleKey = 'values1.title',
  value1TextKey = 'values1.text',
  value2TitleKey = 'values2.title',
  value2TextKey = 'values2.text',
  value3TitleKey = 'values3.title',
  value3TextKey = 'values3.text'
}) {
  const { t } = useTranslation(namespace);

  return (
    <>
      <div className="container">
        <Separator
          animated={true}
          color="#000"
          thickness={1}
          animationDelay={0.1}
        />
      </div>

      <Description
        descriptionTitle={t(value1TitleKey)}
        descriptionText={t(value1TextKey)}
        showButton={false}
        titleTag="h3"
      />

      <div className="container">
        <Separator
          animated={true}
          color="#000"
          thickness={1}
          animationDelay={0.2}
        />
      </div>

      <Description
        descriptionTitle={t(value2TitleKey)}
        descriptionText={t(value2TextKey)}
        showButton={false}
        titleTag="h3"
      />

      <div className="container">
        <Separator
          animated={true}
          color="#000"
          thickness={1}
          animationDelay={0.3}
        />
      </div>

      <Description
        descriptionTitle={t(value3TitleKey)}
        descriptionText={t(value3TextKey)}
        showButton={false}
        titleTag="h3"
      />
    </>
  );
}