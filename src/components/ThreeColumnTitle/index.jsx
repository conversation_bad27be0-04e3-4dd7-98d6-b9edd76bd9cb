import React from 'react';
import Image from 'next/image';
import styles from './style.module.scss';
import { useTranslation } from '@/hooks/useTranslation';
import { motion } from 'framer-motion'
import GSAPTextReveal from '@/components/GSAPTextReveal';
import GSAPParagraphs from '@/components/GSAPTextReveal/GSAPParagraphs';
import { getPreset } from '@/components/GSAPTextReveal/presets';

export default function ThreeColumnTitle({
  locale = 'fr',
  namespace = 'agency',
  titleKey = 'three_column_title.title',
  descriptionKey = 'three_column_title.description',
  image1AltKey = 'three_column_title.image1_alt',
  image2AltKey = 'three_column_title.image2_alt',
  image3AltKey = 'three_column_title.image3_alt'
}) {
  const { t } = useTranslation(namespace);

  return (
    <section className={`${styles.threeColumnTitle} section`}>
      <div className="container">
        <div className={styles.grid}>
          <div className={styles.column}>
            <GSAPTextReveal
              as="h2"
              {...getPreset('hero')}
            >
              {t(titleKey)}
            </GSAPTextReveal>
            <motion.div
              className={styles.imageWrapper}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.6, ease: 'easeInOut', delay: 0.1 }}
            >
              <Image
                src="/images/ordinateur-bureau-macbook.png"
                alt={t(image1AltKey)}
                width={400}
                height={400}
                className={styles.image}
              />
            </motion.div>
          </div>

          <div className={styles.column}>
            <motion.div
              className={styles.imageWrapper}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.6, ease: 'easeInOut', delay: 0.2 }}
            >
              <Image
                src="/images/lucas-joliveau-telephone-assis.png"
                alt={t(image2AltKey)}
                width={600}
                height={400}
                className={styles.image}
              />
            </motion.div>
          </div>

          <div className={styles.column}>
            <motion.div
              className={styles.imageWrapper}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.6, ease: 'easeInOut', delay: 0.3 }}
            >
              <Image
                src="/images/mains-lucas-joliveau-ordinateur-portable.png"
                alt={t(image3AltKey)}
                width={400}
                height={400}
                className={styles.image}
              />
            </motion.div>
            <GSAPParagraphs
              className='text-big'
              preset="lines"
              presetOverrides={{ delay: 0.8, stagger: 0.2 }}
              paragraphSpacing={1.5}
            >
              {t(descriptionKey)}
            </GSAPParagraphs>
          </div>
        </div>
      </div>
    </section>
  );
}
