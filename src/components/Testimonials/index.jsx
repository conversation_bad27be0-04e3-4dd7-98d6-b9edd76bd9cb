'use client';
import { useMemo } from 'react';
import { useTranslation } from '@/hooks/useTranslation';
import styles from './style.module.scss';
import TestimonialCarousel from './components/TestimonialCarousel';

import Hero3 from '@/components/Heros/Hero3';
import AnimatedLink from '@/components/AnimatedLink';
import { motion } from 'framer-motion'



export default function Testimonials({
  locale = 'fr',
  namespace = 'agency',
  sectionLabel = 'testimonials.section_label',
  sectionTitle = 'testimonials.section_title',
  sectionDescription = 'testimonials.section_description',
  sectionDescriptionLinkText = 'testimonials.section_description_link_text',
  viewAllButton = 'testimonials.view_all_button',
  testimonialsData = 'testimonials.testimonials_data'
}) {
  const { t } = useTranslation(namespace);

  // Récupérer les données de témoignages depuis les traductions
  const testimonialsDataArray = useMemo(() => {
    const testimonials = [];
    let index = 0;

    // Boucle pour récupérer tous les témoignages disponibles
    while (true) {
      const name = t(`${testimonialsData}.${index}.name`);
      const content = t(`${testimonialsData}.${index}.content`);

      // Si la traduction retourne la clé au lieu de la valeur, on arrête
      if (name === `${testimonialsData}.${index}.name` ||
          content === `${testimonialsData}.${index}.content`) {
        break;
      }

      testimonials.push({ name, content });
      index++;
    }

    return testimonials;
  }, [t, testimonialsData]);



  // Construire la description avec AnimatedLink
  const descriptionTemplate = t(sectionDescription);
  const linkText = t(sectionDescriptionLinkText);

  return (
    <section className={styles.testimonials}>
      <div className="container">
        <Hero3
          subtitle={t(sectionLabel)}
          title={t(sectionTitle)}
          description={
            <>
              {descriptionTemplate.split('{{link}}')[0]}
              <AnimatedLink href="https://g.page/r/CdYePvCaFA87EBM/review" external>
                {linkText}
              </AnimatedLink>
              {descriptionTemplate.split('{{link}}')[1]}
            </>
          }
          buttonText={t(viewAllButton)}
          buttonLink="https://g.page/r/CdYePvCaFA87EAE/review"
        />
            <motion.div
              className={styles.carouselSection}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, ease: 'easeInOut', delay: 0.7 }}
            >
                <TestimonialCarousel
                  testimonials={testimonialsDataArray}
                  locale={locale}
                />
            </motion.div>
      </div>
    </section>
  );
}
