"use client";

import Hero4 from '@/components/Heros/Hero4';
import Hero3 from '@/components/Heros/Hero3';
import StatsCards from '@/components/StatsCards';
import ThreeColumnTitle from '@/components/ThreeColumnTitle';
import ExpertiseAccordion from '@/components/ExpertiseAccordion';
import Testimonials from '@/components/Testimonials';
import Values from '@/components/Values';
import TitleTextImages from '@/components/TitleTextImages';
import Description from '@/components/Description';
import BasicCenterCTA from '@/components/CallToAction/BasicCenterCTA';
import { useTranslation } from '@/hooks/useTranslation';


export default function AgencyClient({ params }) {
  const locale = params.locale || 'fr';
  const { t } = useTranslation('agency');

  // Données des expertises (en attendant une meilleure solution de traduction)
  const expertisesData = locale === 'fr' ? [
    {
      title: "Création et refonte d'identité",
      items: [
        "Conception de logotypes",
        "Création de chartes graphiques",
        "Design de marchandises",
        "Impression sur-mesure"
      ]
    },
    {
      title: "Stratégie et design thinking",
      items: [
        "Ateliers UX collaboratifs",
        "Analyse concurrentielle",
        "Définition de personas",
        "Optimisation du parcours utilisateur"
      ]
    },
    {
      title: "Développement web",
      items: [
        "Sites vitrine",
        "Boutiques e-commerce",
        "Applications web et portails interactifs",
        "Intégration de systèmes tiers",
        "Optimisation de performance",
        "Référencement naturel"
      ]
    }
  ] : [
    {
      title: "Brand identity creation and redesign",
      items: [
        "Logo creation",
        "Merchandise design",
        "Custom printing"
      ]
    },
    {
      title: "Strategy and design thinking",
      items: [
        "Competitive analysis",
        "Persona definition",
        "User journey"
      ]
    },
    {
      title: "Web development",
      items: [
        "Showcase websites",
        "E-commerce",
        "Web applications"
      ]
    }
  ];

  return (
    <div>
      <Hero4
        title={t('hero4.title')}
        subtitle={t('hero4.subtitle')}
        description={t('hero4.description')}
        imageAlt={t('hero4.image_alt')}
        imgSrc="/images/lucas-joliveau-siege-ordinateur-portable.png"
        locale={locale}
      />
      <div className="container">
              <Hero3
                subtitle={t('hero3.subtitle')}
                title={t('hero3.title')}
                description={t('hero3.description')}
                buttonText={t('hero3.button.text')}
                buttonLink="https://g.page/r/CdYePvCaFA87EAE/review"
                locale={locale}
              />
              </div>
            <StatsCards />
      <TitleTextImages
        title={t('titletextimages.title')}
        description={t('titletextimages.description')}
        image1Alt={t('titletextimages.image1_alt')}
        image2Alt={t('titletextimages.image2_alt')}
      />
      <Description
        descriptionTitle={t('descriptiontext.title')}
        descriptionText={t('descriptiontext.text')}
        showButton={false}
        titleTag="h3"
      />


      <ThreeColumnTitle
        locale={locale}
        title="three_column_title.title"
        description="three_column_title.description"
        image1Alt="three_column_title.image1_alt"
        image2Alt="three_column_title.image2_alt"
        image3Alt="three_column_title.image3_alt"
      />
      <Values
        value1Title="values1.title"
        value1Text="values1.text"
        value2Title="values2.title"
        value2Text="values2.text"
        value3Title="values3.title"
        value3Text="values3.text"
      />
      <Testimonials
        locale={locale}
        sectionLabel="testimonials.section_label"
        sectionTitle="testimonials.section_title"
        sectionDescription="testimonials.section_description"
        sectionDescriptionLinkText="testimonials.section_description_link_text"
        viewAllButton="testimonials.view_all_button"
        testimonialsData="testimonials.testimonials_data"
      />
      <ExpertiseAccordion
        title={locale === 'fr' ? 'Nos expertises' : 'Our expertise'}
        expertises={expertisesData}
      />
<BasicCenterCTA
  backgroundColor="#C8CEC9"
  titleColor="#19271B"
  textColor="#19271B"
  title="Nous réglons vos problèmes grâce au design."
  text="Chaque projet est suivi de bout en bout, avec une communication claire et des délais respectés."
  buttonText="Démarrer un projet"
  buttonHref={`/${locale}/contact`}
  buttonVariant='dark'
/>
    </div>
  );
}
